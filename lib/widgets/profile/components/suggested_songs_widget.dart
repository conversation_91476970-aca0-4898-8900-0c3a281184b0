import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:lottie/lottie.dart';
import '../../../providers/apple_music_provider.dart';
import '../../../providers/spotify_provider.dart';
import '../../../models/music_track.dart';
import '../../../screens/search/ai_search/ai_search_provider.dart';
import '../../../services/ai/global_ai_provider_service.dart';
import 'track_card.dart';
import 'track_card_skeleton.dart';
import 'dart:async';

/// Shared widget for suggested songs functionality
/// Uses the global AI provider for consistent state across the app
class SuggestedSongsWidget extends StatefulWidget {
  final String currentFilter;
  final VoidCallback? onRefresh;
  final Function(MusicTrack)? onTrackPlay;
  final bool isSmallScreen;
  
  const SuggestedSongsWidget({
    Key? key,
    required this.currentFilter,
    this.onRefresh,
    this.onTrackPlay,
    this.isSmallScreen = false,
  }) : super(key: key);

  @override
  State<SuggestedSongsWidget> createState() => _SuggestedSongsWidgetState();
}

class _SuggestedSongsWidgetState extends State<SuggestedSongsWidget> 
    with AutomaticKeepAliveClientMixin {
  
  // Use global AI provider - no more static instances
  List<MusicTrack> _suggestedSongs = [];
  bool _isLoading = false;
  String? _errorMessage;
  DateTime? _lastLoadTime;
  static const Duration _cacheExpiry = Duration(hours: 1);
  
  // Scroll controller for pagination
  final ScrollController _scrollController = ScrollController();
  
  @override
  bool get wantKeepAlive => true;
  
  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    // Set initial loading state
    _isLoading = true;

    // Initialize after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeIfNeeded();
    });
  }
  
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // If we don't have data and we're not loading, try to initialize
    if (_suggestedSongs.isEmpty && !_isLoading) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _initializeIfNeeded();
      });
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }
  
  /// Load suggestions using the global AI provider
  Future<void> _initializeIfNeeded() async {
    // Check if cache is still valid
    if (_suggestedSongs.isNotEmpty &&
        _lastLoadTime != null &&
        DateTime.now().difference(_lastLoadTime!) < _cacheExpiry) {
      if (kDebugMode) {
        print('📦 Using cached suggested songs (${_suggestedSongs.length} tracks)');
      }
      // Still set loading to false if we have cached data
      if (mounted && _isLoading) {
        setState(() {
          _isLoading = false;
        });
      }
      return;
    }

    if (_isLoading && _suggestedSongs.isNotEmpty) return; // Don't reload if we already have data and are loading more

    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Get the global AI provider
      final globalProvider = GlobalAIProviderService.instance.aiProvider;

      if (globalProvider == null) {
        // Global provider not initialized yet, try to initialize
        if (kDebugMode) {
          print('🎯 Global AI provider not ready, attempting initialization...');
        }
        await GlobalAIProviderService.instance.initializeIfAuthenticated(context);

        // Give it a moment to initialize
        await Future.delayed(const Duration(milliseconds: 500));
      }

      final provider = GlobalAIProviderService.instance.aiProvider;
      if (provider == null) {
        if (kDebugMode) {
          print('⚠️ Global AI provider still not available after initialization attempt');
        }
        throw Exception('AI provider not available. Please check your connection.');
      }

      // Ensure we're using artist-based recommendations
      if (provider.currentCategory != 'artistBased') {
        if (kDebugMode) {
          print('🔄 Switching to artist-based recommendations...');
        }
        if (mounted) {
          provider.onCategoryChanged('artistBased', context);
        }
        // Wait for category change to complete
        await Future.delayed(const Duration(milliseconds: 1000));
      }

      // Wait for recommendations to load if they're currently loading
      int attempts = 0;
      while (provider.isLoading && attempts < 10) {
        await Future.delayed(const Duration(milliseconds: 300));
        attempts++;
      }

      if (mounted) {
        setState(() {
          _suggestedSongs = List<MusicTrack>.from(provider.currentRecommendations);
          _isLoading = false; // Always set to false here
          _lastLoadTime = DateTime.now();
        });

        if (kDebugMode) {
          print('✅ Suggested songs loaded from global provider with ${_suggestedSongs.length} tracks');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading suggested songs: $e');
      }
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load suggestions. Please try again.';
          _isLoading = false;
        });
      }
    }
  }
  
  /// Handle scroll for pagination
  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent * 0.8) {
      _loadMoreSuggestions();
    }
  }
  
  /// Load more suggestions using pagination
  Future<void> _loadMoreSuggestions() async {
    final provider = GlobalAIProviderService.instance.aiProvider;
    if (provider == null || _isLoading) return;
    
    try {
      if (kDebugMode) {
        print('🔄 Loading more suggested songs...');
      }
      
      setState(() {
        _isLoading = true;
      });
      
      // Trigger load more on the global AI provider
      await provider.loadMoreRecommendations(context);
      
      // Update our local state with new recommendations
      if (mounted) {
        setState(() {
          _suggestedSongs = List<MusicTrack>.from(provider.currentRecommendations);
          _isLoading = false;
          _lastLoadTime = DateTime.now();
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading more suggestions: $e');
      }
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
  
  /// Shuffle and play all suggested songs
  Future<void> _shuffleSuggestedSongs() async {
    if (_suggestedSongs.isEmpty && !_isLoading) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No suggested songs to shuffle')),
      );
      return;
    }

    try {
      final appleProvider = Provider.of<AppleMusicProvider>(context, listen: false);
      final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);

      // Create a shuffled copy of the suggested songs
      final shuffledTracks = List<MusicTrack>.from(_suggestedSongs);
      shuffledTracks.shuffle();

      bool success = false;

      if (appleProvider.isConnected) {
        // For Apple Music, set up queue with shuffle enabled
        final startIndex = 0; // Always start from first track when shuffling

        success = await appleProvider.playCollection(
          tracks: shuffledTracks,
          collectionType: 'suggested_songs',
          startIndex: startIndex,
          collectionMetadata: {
            'name': 'Suggested Songs',
            'shuffled': true,
          },
        );

        if (success && kDebugMode) {
          print('✅ Apple Music queue set up with ${_suggestedSongs.length} suggested songs');
        }
      } else if (spotifyProvider.isConnected) {
        // For Spotify, use the original callback if provided
        if (widget.onTrackPlay != null && shuffledTracks.isNotEmpty) {
          widget.onTrackPlay!(shuffledTracks.first);
          success = true;
        }
      }

      if (success) {
        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Shuffling ${shuffledTracks.length} suggested songs'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      } else {
        // Show error message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to start shuffle playback'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error shuffling suggested songs: $e');
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error starting shuffle playback'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Clear data (useful for logout or refresh)
  void clearData() {
    if (mounted) {
      setState(() {
        _suggestedSongs.clear();
        _isLoading = false;
        _errorMessage = null;
        _lastLoadTime = null;
      });
      
      if (kDebugMode) {
        print('🗑️ Cleared suggested songs data');
      }
    }
  }
  
  /// Force refresh of suggested songs data from AI provider cache
  Future<void> refreshData() async {
    if (!mounted) return;

    if (kDebugMode) {
      print('🔄 Starting suggested songs refresh...');
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _lastLoadTime = null; // Invalidate local cache
    });

    try {
      // Get the global AI provider
      final provider = GlobalAIProviderService.instance.aiProvider;

      if (provider == null) {
        if (kDebugMode) {
          print('⚠️ Global AI provider not available for refresh');
        }
        throw Exception('AI provider not available. Please check your connection.');
      }

      // Ensure we're on artist-based category for suggested songs
      if (provider.currentCategory != 'artistBased') {
        if (kDebugMode) {
          print('🔄 Switching to artist-based for refresh...');
        }
        if (mounted) {
          provider.onCategoryChanged('artistBased', context);
          // Wait for category change to complete
          await Future.delayed(const Duration(milliseconds: 500));
        }
      }

      // Call the AI provider's refresh method to get fresh content from cache
      if (kDebugMode) {
        print('🎯 Calling AI provider refresh for fresh cached content...');
      }

      if (mounted) {
        await provider.refreshRecommendations(context);
      }

      // Wait for refresh to complete
      int attempts = 0;
      while (provider.isLoading && attempts < 15) {
        await Future.delayed(const Duration(milliseconds: 300));
        attempts++;
      }

      if (mounted) {
        setState(() {
          _suggestedSongs = List<MusicTrack>.from(provider.currentRecommendations);
          _isLoading = false;
          _lastLoadTime = DateTime.now();
        });

        if (kDebugMode) {
          print('✅ Suggested songs refreshed with ${_suggestedSongs.length} fresh tracks from AI provider cache');
        }

        // Show success feedback
        if (mounted && _suggestedSongs.isNotEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Refreshed with ${_suggestedSongs.length} new suggestions!'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error refreshing suggested songs: $e');
      }
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to refresh suggestions. Please try again.';
          _isLoading = false;
        });

        // Show error feedback
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to refresh suggestions. Please try again.'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    // Show loading state
    if (_isLoading && _suggestedSongs.isEmpty) {
      return _buildLoadingState();
    }
    
    // Show error state
    if (_errorMessage != null && _suggestedSongs.isEmpty) {
      return _buildErrorState();
    }
    
    // Show empty state
    if (_suggestedSongs.isEmpty) {
      return _buildEmptyState();
    }
    
    // Show content
    return _buildContent();
  }
  
  Widget _buildLoadingState() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      itemCount: 10,
      itemBuilder: (context, index) {
        return TrackCardSkeleton(isSmallScreen: widget.isSmallScreen);
      },
    );
  }
  
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.red.withValues(alpha: 0.8),
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading suggestions',
            style: Theme.of(context).textTheme.titleMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            _errorMessage ?? 'Unknown error',
            style: TextStyle(
              color: Colors.grey.withValues(alpha: 0.7),
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: refreshData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }
  
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.auto_awesome,
            size: 48,
            color: Colors.grey.withValues(alpha: 0.6),
          ),
          const SizedBox(height: 16),
          Text(
            'No suggestions available',
            style: Theme.of(context).textTheme.titleMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Try listening to more music to get personalized recommendations!',
            style: TextStyle(
              color: Colors.grey.withValues(alpha: 0.7),
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
  
  Widget _buildContent() {
    return RefreshIndicator(
      onRefresh: refreshData,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        itemCount: _suggestedSongs.length + 1 + (_isLoading ? 1 : 0),
        itemBuilder: (context, index) {
          // Add shuffle header at the top
          if (index == 0) {
            return _buildShuffleHeader();
          }
          
          // Adjust index for header
          final dataIndex = index - 1;
          
          // Show loading indicator at the end if loading
          if (dataIndex == _suggestedSongs.length) {
            if (_isLoading) {
              return Padding(
                padding: const EdgeInsets.all(16.0),
                child: Center(
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              );
            }
            return const SizedBox.shrink();
          }
          
          // Show track card
          final track = _suggestedSongs[dataIndex];
          return TrackCard(
            track: track,
            isSmallScreen: widget.isSmallScreen,
            onTap: () async {
              // Use Apple Music queue manager for individual track playback
              final appleProvider = Provider.of<AppleMusicProvider>(context, listen: false);
              final spotifyProvider = Provider.of<SpotifyProvider>(context, listen: false);

              if (appleProvider.isConnected) {
                // For Apple Music, set up queue with all suggested songs starting from selected track
                final trackIndex = _suggestedSongs.indexOf(track);
                if (trackIndex != -1) {
                  final queueManager = appleProvider.queueManager;
                  await queueManager.setQueue(
                    tracks: _suggestedSongs,
                    collectionType: 'suggested_songs',
                    startIndex: trackIndex,
                    collectionMetadata: {
                      'name': 'Suggested Songs',
                      'shuffled': false,
                    },
                  );
                }
              } else if (spotifyProvider.isConnected && widget.onTrackPlay != null) {
                // Fallback to original callback for Spotify
                widget.onTrackPlay!(track);
              }
            },
          );
        },
      ),
    );
  }
  
  Widget _buildShuffleHeader() {
    return Builder(
      builder: (context) => Container(
        margin: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      child: Row(
        children: [
            // Add Lottie animation to the left of the title
            SizedBox(
              width: 30,
              height: 30,
              child: Lottie.asset(
                'assets/anim/ai_search.json',
                fit: BoxFit.contain,
                repeat: true,
                animate: true,
              ),
          ),
            const SizedBox(width: 8),
          Expanded(
              child: Text(
                'BOP AI Curated',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
            ),
            Container(
            margin: const EdgeInsets.only(left: 8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
          ),
            child: IconButton(
            icon: Icon(
                Icons.shuffle_rounded,
                size: 18,
              color: Theme.of(context).colorScheme.primary,
            ),
              onPressed: _shuffleSuggestedSongs,
              tooltip: 'Shuffle suggested songs',
            ),
          ),
        ],
      ),
    ));
  }
}
